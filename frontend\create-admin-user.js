#!/usr/bin/env node

// Simple script to create admin user for JuniorCars CMS
// Run with: node create-admin-user.js

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    console.log('🚀 Creating admin user...')

    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingAdmin) {
      console.log('👤 Admin user already exists:', existingAdmin.email)
      console.log('🔑 Try logging in with:')
      console.log('   Email: <EMAIL>')
      console.log('   Password: admin123')
      return
    }

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
      }
    })

    console.log('✅ Admin user created successfully!')
    console.log('👤 User details:')
    console.log('   Email:', adminUser.email)
    console.log('   Name:', adminUser.firstName, adminUser.lastName)
    console.log('   Role:', adminUser.role)
    console.log('')
    console.log('🔑 Login credentials:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')
    console.log('')
    console.log('🎉 You can now log in to the CMS!')

  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message)
    
    if (error.message.includes('Query Engine')) {
      console.log('')
      console.log('💡 This might be a Prisma client issue. Try running:')
      console.log('   npx prisma generate')
      console.log('   npm run dev')
    }
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
createAdminUser().catch(console.error)
