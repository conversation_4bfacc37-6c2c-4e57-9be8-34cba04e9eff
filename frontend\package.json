{"name": "car-next-strapi", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.16.2", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "eslint": "^8", "eslint-config-next": "14.2.5", "framer-motion": "^11.0.0", "lucide-react": "^0.400.0", "next": "14.2.5", "next-auth": "^4.24.11", "postcss": "^8", "prisma": "^6.16.2", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.63.0", "swiper": "^11.0.0", "tailwindcss": "^3.4.1", "typescript": "^5", "zod": "^4.1.11"}}