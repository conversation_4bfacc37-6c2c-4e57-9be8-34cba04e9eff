// JuniorCars Custom CMS Database Schema
// Built for budget-friendly, mobile-first automotive website

generator client {
  provider = "prisma-client-js"
  output   = "../lib/prisma-client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Users table for admin authentication
model User {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String   @map("password_hash")
  role         String   @default("admin")
  firstName    String?  @map("first_name")
  lastName     String?  @map("last_name")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("users")
}

// Pages table (replaces Strapi pages)
model Page {
  id           String   @id @default(cuid())
  title        String
  slug         String   @unique
  content      Json? // Rich text content
  heroData     Json?    @map("hero_data") // Hero section data
  carouselData Json?    @map("carousel_data") // Carousel images data
  seoData      Json?    @map("seo_data") // SEO metadata
  published    Boolean  @default(false)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  contentBlocks ContentBlock[]

  @@map("pages")
}

// Navigation items for menu management
model NavigationItem {
  id         String   @id @default(cuid())
  label      String
  url        String?
  parentId   String?  @map("parent_id")
  orderIndex Int      @default(0) @map("order_index")
  isActive   Boolean  @default(true) @map("is_active")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Self-referencing relation for nested menus
  parent   NavigationItem?  @relation("NavigationHierarchy", fields: [parentId], references: [id])
  children NavigationItem[] @relation("NavigationHierarchy")

  @@map("navigation_items")
}

// Car series (replaces Strapi car series)
model CarSeries {
  id             String   @id @default(cuid())
  name           String
  slug           String   @unique
  description    String?
  specifications Json? // Car specifications as JSON
  price          Float?
  heroData       Json?    @map("hero_data") // Hero section data
  carouselData   Json?    @map("carousel_data") // Carousel images data
  published      Boolean  @default(false)
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  @@map("car_series")
}

// Media/Images management
model Media {
  id           String   @id @default(cuid())
  filename     String
  originalName String   @map("original_name")
  url          String
  altText      String?  @map("alt_text")
  size         Int? // File size in bytes
  mimeType     String?  @map("mime_type")
  width        Int?
  height       Int?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("media")
}

// Content blocks for flexible page building
model ContentBlock {
  id         String   @id @default(cuid())
  pageId     String   @map("page_id")
  type       String // 'hero', 'carousel', 'text', 'image', etc.
  data       Json // Block-specific data
  orderIndex Int      @default(0) @map("order_index")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  page Page @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("content_blocks")
}
