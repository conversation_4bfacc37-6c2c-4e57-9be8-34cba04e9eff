# Project Documentation: JuniorCars

## 1. Project Overview

This project is a Next.js web application for a premium car collection called "JuniorCarsUk". It's designed to be a budget-friendly, mobile-first automotive website that showcases a collection of classic and modern Junior Cars. The application fetches data from a custom-built CMS and includes features like user authentication and a responsive design.

## 2. Tech Stack

- **Framework:** [Next.js](https://nextjs.org/) 14
- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **Styling:** [Tailwind CSS](https://tailwindcss.com/)
- **Database:**
  - **Development:** [SQLite](https://www.sqlite.org/index.html)
  - **Production:** [PostgreSQL](https://www.postgresql.org/) hosted on [Supabase](https://supabase.com/)
- **ORM:** [Prisma](https://www.prisma.io/)
- **Authentication:** [NextAuth.js](https://next-auth.js.org/)
- **API Communication:** [Axios](https://axios-http.com/) and native `fetch`

## 3. Project Structure

```
/
├── .env                # Environment variables for Supabase
├── .env.local          # Local environment variables
├── .env.production     # Production environment variables
├── next.config.js      # Next.js configuration
├── prisma/
│   ├── dev.db          # SQLite database for development
│   └── schema.prisma   # Prisma schema for database models
├── public/             # Public assets (images, fonts, etc.)
├── src/
│   ├── app/            # Next.js App Router pages
│   ├── components/     # Reusable React components
│   ├── lib/            # Library files, including the CMS service
│   └── types/          # TypeScript type definitions
└── scripts/            # Node.js scripts for various tasks
```

### Key Files

- `src/lib/cms-service.ts`: This service is responsible for fetching data from the custom CMS API. It replaces the need for a traditional CMS like Strapi.
- `prisma/schema.prisma`: Defines the database schema for all models, including `User`, `Page`, `CarSeries`, and `NavigationItem`.
- `src/app/page.tsx`: The main entry point for the homepage.
- `src/components/PageTemplate.tsx`: A reusable component for rendering pages with data from the CMS.

## 4. Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/en/) (v18 or later)
- [npm](https://www.npmjs.com/)

### Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    ```
2.  **Navigate to the project directory:**
    ```bash
    cd cars-next-strapi/frontend
    ```
3.  **Install dependencies:**
    ```bash
    npm install
    ```

### Running the Development Server

1.  **Set up your local environment variables:**
    Create a `.env.local` file by copying the `.env.local.example` file. For local development, the `DATABASE_URL` in `prisma/schema.prisma` is set to use the SQLite database, so you don't need to change it unless you want to connect to a different database.

2.  **Generate the Prisma client:**
    ```bash
    npx prisma generate
    ```

3.  **Start the development server:**
    ```bash
    npm run dev
    ```

The application will be available at [http://localhost:3000](http://localhost:3000).

## 5. Database

This project uses two databases:

-   **SQLite** for local development. The database file is `prisma/dev.db`. This is configured in the `datasource db` block in `prisma/schema.prisma`.
-   **PostgreSQL** hosted on [Supabase](https://supabase.com/) for the production environment. The connection string for the production database is stored in the `.env.production` file.

The `scripts/migrate-to-production.js` script can be used to help migrate data from the development database to the production database.

## 6. Custom CMS

The application uses a custom-built CMS for content management. The `src/lib/cms-service.ts` file contains the logic for interacting with the CMS API. This service fetches data for pages, car series, navigation, and media.

The CMS provides the following data models:
-   **Pages:** For creating content pages like "About Us" or "Contact".
-   **Car Series:** For managing the different car collections.
-   **Navigation:** For managing the website's navigation menu.
-   **Media:** For managing images and other media assets.

If the CMS is unavailable, the application uses fallback data defined in `src/lib/cms-service.ts` to ensure that the site remains functional.

## 7. Authentication

Authentication is handled using [NextAuth.js](https://next-auth.js.org/). The configuration is located in `src/app/api/auth/[...nextauth]/route.ts`. It uses the `CredentialsProvider` to allow users to sign in with their email and password.

User data is stored in the `User` model, which is defined in the `prisma/schema.prisma` file.

## 8. Deployment

To deploy the application to a production environment, you will need to:

1.  **Set up a PostgreSQL database on Supabase:**
    - Create a new project on Supabase.
    - In the project's database settings, get the connection string.

2.  **Configure environment variables:**
    - Create a `.env.production` file.
    - Add the `DATABASE_URL` from your Supabase project to this file.
    - Add any other required environment variables, such as `NEXTAUTH_SECRET` and `NEXT_PUBLIC_SITE_URL`.

3.  **Build the application:**
    ```bash
    npm run build
    ```

4.  **Start the production server:**
    ```bash
    npm run start
    ```

It is recommended to deploy the application on a platform like [Vercel](https://vercel.com/) or [Netlify](https://www.netlify.com/), which provide seamless integration with Next.js applications.
